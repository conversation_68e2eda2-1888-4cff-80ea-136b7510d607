```mermaid
flowchart TD
    %% Input and Configuration
    START([🚀 Start Code2Tutor Workflow]) --> CONFIG{📋 Validate Configuration}
    CONFIG -->|Valid| SHARED[📦 SharedStore<br/>• user_id<br/>• repo_url/local_dir<br/>• selected_files<br/>• target_audience<br/>• content_language<br/>• tutorial_format<br/>• max_concepts]
    CONFIG -->|Invalid| ERROR_CONFIG[❌ Configuration Error]
    
    %% Repository Analysis Stage
    SHARED --> REPO_AGENT[🔍 RepoAnalysisAgent]
    REPO_AGENT --> REPO_VALIDATE{✅ Validate Inputs}
    REPO_VALIDATE -->|repo_url exists| GITHUB_FETCH[📥 fetch_selected_github_files]
    REPO_VALIDATE -->|local_dir exists| LOCAL_FETCH[📁 fetch_selected_local_files]
    REPO_VALIDATE -->|Neither exists| ERROR_REPO[❌ Repository Error]
    GITHUB_FETCH --> REPO_FILTER[🔧 filterEducationalFiles]
    LOCAL_FETCH --> REPO_FILTER
    REPO_FILTER --> REPO_ANALYZE[📊 analyzeRepoStructure]
    REPO_ANALYZE --> REPO_UPDATE[📝 Update SharedStore<br/>• files: [string, string][]<br/>• structure: RepoStructure<br/>• languages: string[]]
    
    %% Concept Extraction Stage
    REPO_UPDATE --> CONCEPT_AGENT[🧠 ConceptExtractionAgent]
    CONCEPT_AGENT --> CONCEPT_VALIDATE{✅ Validate Files}
    CONCEPT_VALIDATE -->|Files exist| CONCEPT_PREP[📝 Prepare Context<br/>• prepareCodeContext()<br/>• createFileListing()]
    CONCEPT_VALIDATE -->|No files| ERROR_CONCEPT[❌ Concept Error]
    
    CONCEPT_PREP --> CONCEPT_PROMPT[📋 Build Prompt<br/>CONCEPT_EXTRACTION_PROMPT<br/>• project_name<br/>• context<br/>• max_concepts<br/>• target_audience]
    
    %% LLM Call 1 - Concept Extraction
    CONCEPT_PROMPT --> LLM1[🤖 LLM Call #1<br/>callLlm_openrouter<br/>Model: gemini-2.5-flash<br/>Temperature: 0.7<br/>Purpose: Extract Learning Concepts]
    LLM1 --> CONCEPT_PARSE[🔍 Parse YAML Response<br/>parseConceptsFromResponse()]
    CONCEPT_PARSE --> CONCEPT_VALIDATE_RESULT{✅ Valid Concepts?}
    CONCEPT_VALIDATE_RESULT -->|Valid| CONCEPT_ENHANCE[⚡ validateAndEnhanceConcepts]
    CONCEPT_VALIDATE_RESULT -->|Invalid| CONCEPT_RETRY{🔄 Retry Available?}
    CONCEPT_RETRY -->|Yes| CONCEPT_PROMPT
    CONCEPT_RETRY -->|No| ERROR_CONCEPT_FINAL[❌ Concept Extraction Failed]
    
    CONCEPT_ENHANCE --> CONCEPT_UPDATE[📝 Update SharedStore<br/>• concepts: LearningConcept[]<br/>• conceptCount: number]
    
    %% Tutorial Planning Stage
    CONCEPT_UPDATE --> PLANNING_AGENT[📚 TutorialPlanningAgent]
    PLANNING_AGENT --> PLANNING_VALIDATE{✅ Validate Concepts}
    PLANNING_VALIDATE -->|Concepts exist| PLANNING_PREP[📝 Prepare Planning Context<br/>• formatConceptsForPlanning()<br/>• createLearningObjectives()]
    PLANNING_VALIDATE -->|No concepts| ERROR_PLANNING[❌ Planning Error]
    
    PLANNING_PREP --> PLANNING_PROMPT[📋 Build Prompt<br/>TUTORIAL_PLANNING_PROMPT<br/>• concepts<br/>• target_audience<br/>• tutorial_format<br/>• learning_objectives]
    
    %% LLM Call 2 - Tutorial Planning
    PLANNING_PROMPT --> LLM2[🤖 LLM Call #2<br/>callLlm_openrouter<br/>Model: gemini-2.5-flash<br/>Temperature: 0.5<br/>Purpose: Structure Tutorial Plan]
    LLM2 --> PLANNING_PARSE[🔍 Parse Tutorial Structure<br/>parseTutorialStructure()]
    PLANNING_PARSE --> PLANNING_VALIDATE_RESULT{✅ Valid Structure?}
    PLANNING_VALIDATE_RESULT -->|Valid| PLANNING_ENHANCE[⚡ enhanceTutorialStructure]
    PLANNING_VALIDATE_RESULT -->|Invalid| PLANNING_RETRY{🔄 Retry Available?}
    PLANNING_RETRY -->|Yes| PLANNING_PROMPT
    PLANNING_RETRY -->|No| ERROR_PLANNING_FINAL[❌ Planning Failed]
    
    PLANNING_ENHANCE --> PLANNING_UPDATE[📝 Update SharedStore<br/>• tutorial_structure: TutorialStructure<br/>• sections: TutorialSection[]<br/>• learning_path: string[]]
    
    %% Content Generation Stage
    PLANNING_UPDATE --> CONTENT_AGENT[✍️ ContentGenerationAgent]
    CONTENT_AGENT --> CONTENT_VALIDATE{✅ Validate Structure}
    CONTENT_VALIDATE -->|Structure exists| CONTENT_LOOP[🔄 For Each Section]
    CONTENT_VALIDATE -->|No structure| ERROR_CONTENT[❌ Content Error]
    
    CONTENT_LOOP --> CONTENT_PREP[📝 Prepare Section Context<br/>• prepareSectionContext()<br/>• gatherRelevantCode()]
    CONTENT_PREP --> CONTENT_PROMPT[📋 Build Content Prompt<br/>CONTENT_GENERATION_PROMPT<br/>• section_info<br/>• relevant_code<br/>• examples_needed]
    
    %% LLM Call 3 - Content Generation
    CONTENT_PROMPT --> LLM3[🤖 LLM Call #3<br/>callLlm_openrouter<br/>Model: gemini-2.5-flash<br/>Temperature: 0.6<br/>Purpose: Generate Section Content]
    LLM3 --> CONTENT_PARSE[🔍 Parse Section Content<br/>parseSectionContent()]
    
    %% Exercise Generation (if enabled)
    CONTENT_PARSE --> EXERCISE_CHECK{🎯 Include Exercises?}
    EXERCISE_CHECK -->|Yes| EXERCISE_PROMPT[📋 Build Exercise Prompt<br/>EXERCISE_GENERATION_PROMPT<br/>• section_content<br/>• difficulty_level<br/>• exercise_types]
    EXERCISE_CHECK -->|No| CONTENT_FINALIZE
    
    %% LLM Call 4 - Exercise Generation
    EXERCISE_PROMPT --> LLM4[🤖 LLM Call #4<br/>callLlm_openrouter<br/>Model: gemini-2.5-flash<br/>Temperature: 0.7<br/>Purpose: Generate Interactive Exercises]
    LLM4 --> EXERCISE_PARSE[🔍 Parse Exercises<br/>parseExercises()]
    EXERCISE_PARSE --> CONTENT_FINALIZE[📝 Finalize Section<br/>• content<br/>• exercises<br/>• examples]
    
    CONTENT_FINALIZE --> CONTENT_MORE{🔄 More Sections?}
    CONTENT_MORE -->|Yes| CONTENT_LOOP
    CONTENT_MORE -->|No| CONTENT_UPDATE[📝 Update SharedStore<br/>• generated_sections: TutorialSection[]<br/>• exercises: Exercise[]<br/>• examples: CodeExample[]]
    
    %% Tutorial Assembly Stage
    CONTENT_UPDATE --> ASSEMBLY_AGENT[🔧 TutorialAssemblyAgent]
    ASSEMBLY_AGENT --> ASSEMBLY_VALIDATE{✅ Validate Generated Content}
    ASSEMBLY_VALIDATE -->|Content exists| ASSEMBLY_PREP[📝 Prepare Assembly Context<br/>• formatTutorialMetadata()<br/>• organizeSections()]
    ASSEMBLY_VALIDATE -->|No content| ERROR_ASSEMBLY[❌ Assembly Error]
    
    ASSEMBLY_PREP --> ASSEMBLY_PROMPT[📋 Build Assembly Prompt<br/>TUTORIAL_ASSEMBLY_PROMPT<br/>• tutorial_metadata<br/>• sections_content<br/>• final_format]
    
    %% LLM Call 5 - Tutorial Assembly
    ASSEMBLY_PROMPT --> LLM5[🤖 LLM Call #5<br/>callLlm_openrouter<br/>Model: gemini-2.5-flash<br/>Temperature: 0.3<br/>Purpose: Assemble Final Tutorial]
    LLM5 --> ASSEMBLY_PARSE[🔍 Parse Final Tutorial<br/>parseFinalTutorial()]
    ASSEMBLY_PARSE --> ASSEMBLY_VALIDATE_RESULT{✅ Valid Tutorial?}
    ASSEMBLY_VALIDATE_RESULT -->|Valid| ASSEMBLY_FINALIZE[⚡ finalizeTutorial]
    ASSEMBLY_VALIDATE_RESULT -->|Invalid| ASSEMBLY_RETRY{🔄 Retry Available?}
    ASSEMBLY_RETRY -->|Yes| ASSEMBLY_PROMPT
    ASSEMBLY_RETRY -->|No| ERROR_ASSEMBLY_FINAL[❌ Assembly Failed]
    
    ASSEMBLY_FINALIZE --> ASSEMBLY_UPDATE[📝 Update SharedStore<br/>• final_tutorial: string<br/>• tutorial_metadata: TutorialMetadata<br/>• completion_status: 'completed']
    
    %% Success Path
    ASSEMBLY_UPDATE --> SUCCESS[🎉 Tutorial Generated Successfully]
    SUCCESS --> SAVE_TUTORIAL[💾 Save to Supabase<br/>• tutorial_content<br/>• metadata<br/>• user_id]
    SAVE_TUTORIAL --> COMPLETE([✅ Workflow Complete])
    
    %% Error Handling and Coordination
    ERROR_CONFIG --> COORDINATOR[🎯 Coordinator Agent]
    ERROR_REPO --> COORDINATOR
    ERROR_CONCEPT --> COORDINATOR
    ERROR_CONCEPT_FINAL --> COORDINATOR
    ERROR_PLANNING --> COORDINATOR
    ERROR_PLANNING_FINAL --> COORDINATOR
    ERROR_CONTENT --> COORDINATOR
    ERROR_ASSEMBLY --> COORDINATOR
    ERROR_ASSEMBLY_FINAL --> COORDINATOR
    
    COORDINATOR --> ERROR_ANALYSIS[🔍 Analyze Error<br/>• error_type<br/>• error_stage<br/>• retry_possible]
    ERROR_ANALYSIS --> ERROR_DECISION{🤔 Recovery Possible?}
    ERROR_DECISION -->|Yes| ERROR_RECOVERY[🔄 Attempt Recovery]
    ERROR_DECISION -->|No| ERROR_FINAL[❌ Workflow Failed]
    
    ERROR_RECOVERY --> SHARED
    ERROR_FINAL --> MEMORY_AGENT[🧠 Memory Agent<br/>Store Error Context]
    
    %% Event System (runs parallel to main flow)
    REPO_AGENT -.-> EVENT_PROGRESS[📡 Progress Events]
    CONCEPT_AGENT -.-> EVENT_PROGRESS
    PLANNING_AGENT -.-> EVENT_PROGRESS
    CONTENT_AGENT -.-> EVENT_PROGRESS
    ASSEMBLY_AGENT -.-> EVENT_PROGRESS
    
    REPO_AGENT -.-> EVENT_STATUS[📡 Agent Status Events]
    CONCEPT_AGENT -.-> EVENT_STATUS
    PLANNING_AGENT -.-> EVENT_STATUS
    CONTENT_AGENT -.-> EVENT_STATUS
    ASSEMBLY_AGENT -.-> EVENT_STATUS
    
    ERROR_CONFIG -.-> EVENT_ERROR[📡 Error Events]
    ERROR_REPO -.-> EVENT_ERROR
    ERROR_CONCEPT -.-> EVENT_ERROR
    ERROR_PLANNING -.-> EVENT_ERROR
    ERROR_CONTENT -.-> EVENT_ERROR
    ERROR_ASSEMBLY -.-> EVENT_ERROR
    
    EVENT_PROGRESS -.-> UI_UPDATE[🖥️ UI Updates<br/>• Progress bars<br/>• Status messages<br/>• Activity log]
    EVENT_STATUS -.-> UI_UPDATE
    EVENT_ERROR -.-> UI_UPDATE
    
    SUCCESS -.-> EVENT_COMPLETE[📡 Completion Event]
    EVENT_COMPLETE -.-> UI_UPDATE
    
    %% Styling
    classDef llmCall fill:#ff6b6b,stroke:#d63031,stroke-width:3px,color:#fff
    classDef agent fill:#74b9ff,stroke:#0984e3,stroke-width:2px,color:#fff
    classDef dataStore fill:#55a3ff,stroke:#2d3436,stroke-width:2px,color:#fff
    classDef errorNode fill:#fd79a8,stroke:#e84393,stroke-width:2px,color:#fff
    classDef eventNode fill:#fdcb6e,stroke:#e17055,stroke-width:2px,color:#2d3436
    classDef successNode fill:#00b894,stroke:#00a085,stroke-width:2px,color:#fff
    classDef startEnd fill:#6c5ce7,stroke:#5f3dc4,stroke-width:3px,color:#fff
    
    class LLM1,LLM2,LLM3,LLM4,LLM5 llmCall
    class REPO_AGENT,CONCEPT_AGENT,PLANNING_AGENT,CONTENT_AGENT,ASSEMBLY_AGENT,COORDINATOR,MEMORY_AGENT agent
    class SHARED,REPO_UPDATE,CONCEPT_UPDATE,PLANNING_UPDATE,CONTENT_UPDATE,ASSEMBLY_UPDATE dataStore
    class ERROR_CONFIG,ERROR_REPO,ERROR_CONCEPT,ERROR_CONCEPT_FINAL,ERROR_PLANNING,ERROR_PLANNING_FINAL,ERROR_CONTENT,ERROR_ASSEMBLY,ERROR_ASSEMBLY_FINAL,ERROR_FINAL errorNode
    class EVENT_PROGRESS,EVENT_STATUS,EVENT_ERROR,EVENT_COMPLETE,UI_UPDATE eventNode
    class SUCCESS,SAVE_TUTORIAL,COMPLETE successNode
    class START startEnd
'''
      




