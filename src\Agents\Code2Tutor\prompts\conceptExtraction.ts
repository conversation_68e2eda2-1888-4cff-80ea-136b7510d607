export const CONCEPT_EXTRACTION_PROMPT = `For the project \${project_name}:

Codebase Context:
\${context}

\${language_instruction}Analyze the codebase to identify key learning concepts that would be valuable for someone learning this technology/framework.

Your goal is to extract 5-\${max_concepts} core learning concepts that:
1. Are fundamental to understanding how this codebase works
2. Build upon each other in a logical learning progression
3. Are appropriate for \${target_audience} level learners
4. Can be taught through practical examples from this code

For each learning concept, provide:
1. A clear \`name\` that describes the concept\${name_lang_hint}
2. A \`description\` explaining what this concept teaches (100-150 words)\${desc_lang_hint}
3. The \`difficulty\` level: beginner, intermediate, or advanced
4. A list of \`prerequisites\` (other concept names that should be learned first)
5. Relevant \`file_indices\` (integers) that demonstrate this concept
6. Practical \`examples\` of how this concept is used in the codebase

List of file indices and paths present in the context:
\${file_listing_for_prompt}

Focus on concepts that:
- Have clear, demonstrable examples in the code
- Build practical skills
- Are essential for understanding the project's architecture
- Can be learned through hands-on exercises

Format the output as a YAML list of dictionaries:

\`\`\`yaml
concepts:
  - name: "Concept Name"
    description: "What this concept teaches and why it's important for learners..."
    difficulty: "beginner|intermediate|advanced"
    prerequisites: ["prerequisite concept names"]
    file_indices: [1, 3, 5]
    examples: ["Specific example of how this concept is used", "Another example"]
\`\`\`

Ensure concepts are ordered from foundational to advanced, and that prerequisites reference earlier concepts in the list.`;

export const TUTORIAL_PLANNING_PROMPT = `For the project \${project_name}:

Learning Concepts Identified:
\${concepts_yaml}

Target Audience: \${target_audience}
Tutorial Format: \${tutorial_format}
Include Exercises: \${include_exercises}
Include Diagrams: \${include_diagrams}

\${language_instruction}Create a comprehensive tutorial structure that teaches these concepts effectively.

Design a tutorial that:
1. Follows a logical learning progression
2. Builds skills incrementally
3. Includes practical, hands-on learning
4. Is appropriate for \${target_audience} learners

For the tutorial structure, provide:

1. **Tutorial Metadata:**
   - Title (engaging and descriptive)
   - Description (what learners will achieve)
   - Estimated completion time in minutes
   - Learning objectives (3-5 specific skills/knowledge)
   - Prerequisites (external knowledge needed)

2. **Learning Progression:**
   - Order the concepts for optimal learning flow
   - Identify concept relationships (prerequisite, builds-on, related)
   - Plan how concepts connect and reinforce each other

3. **Section Planning:**
   For each concept, plan a tutorial section with:
   - Section title and learning goals
   - Key points to cover
   - Code examples to include
   - Exercises/activities (if enabled)
   - Estimated time for this section

Format as YAML:

\`\`\`yaml
tutorial_metadata:
  title: "Tutorial Title"
  description: "What learners will accomplish..."
  estimated_time: 120
  target_audience: "\${target_audience}"
  learning_objectives:
    - "Specific skill 1"
    - "Specific skill 2"
  prerequisites:
    - "External knowledge needed"

concept_relationships:
  - from: "Concept A"
    to: "Concept B"
    type: "prerequisite"
    strength: 0.9

progression_path:
  - "concept_1_id"
  - "concept_2_id"

sections:
  - id: "section_1"
    concept: "Concept Name"
    title: "Section Title"
    learning_goals:
      - "What learners will understand"
    key_points:
      - "Important point 1"
      - "Important point 2"
    code_examples:
      - "Brief description of example 1"
    exercises:
      - "Exercise description"
    estimated_time: 25
\`\`\``;

export const CONTENT_GENERATION_PROMPT = `\${language_instruction}Create an engaging tutorial section for the concept "\${concept_name}" in the project \${project_name}.

Section Details:
- Title: \${section_title}
- Target Audience: \${target_audience}
- Tutorial Format: \${tutorial_format}
- Estimated Time: \${estimated_time} minutes

Learning Goals:
\${learning_goals}

Key Points to Cover:
\${key_points}

Relevant Code Context:
\${code_context}

Previous Sections Context:
\${previous_context}

Create a comprehensive tutorial section that includes:

1. **Introduction** (2-3 paragraphs)
   - Hook the learner with a practical problem this concept solves
   - Explain why this concept is important
   - Preview what they'll learn and build

2. **Core Concept Explanation** (3-4 paragraphs)
   - Break down the concept into digestible parts
   - Use analogies and real-world examples
   - Connect to learner's existing knowledge

3. **Practical Examples** (3-5 code examples)
   - Start with simple, minimal examples
   - Build complexity gradually
   - Each example should be under 10 lines
   - Include expected output or behavior
   - Explain each example thoroughly

4. **Hands-on Exercise** (if \${include_exercises})
   - Practical coding challenge
   - Clear instructions and expected outcome
   - Hints for common issues
   - Solution explanation

5. **Deep Dive** (2-3 paragraphs)
   - How it works under the hood
   - Best practices and common patterns
   - When and why to use this concept

6. **Visual Aids** (if \${include_diagrams})
   - Mermaid diagrams to illustrate concepts
   - Keep diagrams simple and focused
   - Maximum 5 components per diagram

7. **Summary and Next Steps**
   - Recap key learnings
   - Connect to upcoming concepts
   - Encourage practice and exploration

Guidelines:
- Write in a friendly, encouraging tone
- Use "you" to address the learner directly
- Include plenty of code comments for clarity
- Break complex ideas into smaller chunks
- Provide context for why each step matters
- Use markdown formatting for readability

Output the complete tutorial section in Markdown format.`;
