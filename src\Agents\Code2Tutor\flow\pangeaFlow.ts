// src/Agents/Code2Tutor/flow/pangeaFlow.ts

import { 
  WorkflowBuilder, 
  WorkflowOrchestrator, 
  ReasoningAgent,
  ToolAgent,
  MemoryAgent 
} from '../../../pangeaflow';
import { SharedStore } from '../types';
import { 
  RepoAnalysisAgent,
  ConceptExtractionAgent,
  TutorialPlanningAgent,
  ContentGenerationAgent,
  TutorialAssemblyAgent
} from '../agents';
import { callLlm_openrouter } from '../../shared/callLlm_openrouter';

/**
 * Creates and configures the Code2Tutor PangeaFlow workflow
 * 
 * This function sets up the complete workflow for transforming code into educational tutorials:
 * 1. Repository Analysis - Fetch and analyze code files
 * 2. Concept Extraction - Identify key learning concepts
 * 3. Tutorial Planning - Structure the learning progression
 * 4. Content Generation - Create educational content
 * 5. Tutorial Assembly - Combine into final tutorial
 */
export function createCode2TutorFlow(): WorkflowOrchestrator {
  const builder = WorkflowBuilder.create();

  // Configure LLM provider for reasoning agents
  const llmProvider = async (prompt: string, context: Record<string, unknown>) => {
    const shared = context.shared as SharedStore;
    
    return await callLlm_openrouter({
      tutorial_id: shared?.tutorial_id,
      prompt,
      temperature: 0.7,
      model: "google/gemini-2.5-flash-preview-05-20",
      use_cache: shared?.use_cache || false,
      user_id: shared?.user_id || 'anonymous'
    });
  };

  // Add specialized agents for the Code2Tutor workflow
  builder
    // Repository Analysis Agent
    .addToolAgent('repo-analysis', {
      analyzeRepository: async (args: any) => {
        const agent = new RepoAnalysisAgent(
          builder['orchestrator']['eventBus'],
          builder['orchestrator']['telemetry']
        );
        return agent.execute({
          id: crypto.randomUUID(),
          startTime: Date.now(),
          metadata: args,
          trace: [],
          state: new Map(),
          events: []
        });
      }
    })

    // Concept Extraction Agent
    .addToolAgent('concept-extraction', {
      extractConcepts: async (args: any) => {
        const agent = new ConceptExtractionAgent(
          builder['orchestrator']['eventBus'],
          builder['orchestrator']['telemetry']
        );
        return agent.execute({
          id: crypto.randomUUID(),
          startTime: Date.now(),
          metadata: args,
          trace: [],
          state: new Map(),
          events: []
        });
      }
    })

    // Tutorial Planning Agent
    .addToolAgent('tutorial-planning', {
      planTutorial: async (args: any) => {
        const agent = new TutorialPlanningAgent(
          builder['orchestrator']['eventBus'],
          builder['orchestrator']['telemetry']
        );
        return agent.execute({
          id: crypto.randomUUID(),
          startTime: Date.now(),
          metadata: args,
          trace: [],
          state: new Map(),
          events: []
        });
      }
    })

    // Content Generation Agent
    .addToolAgent('content-generation', {
      generateContent: async (args: any) => {
        const agent = new ContentGenerationAgent(
          builder['orchestrator']['eventBus'],
          builder['orchestrator']['telemetry']
        );
        return agent.execute({
          id: crypto.randomUUID(),
          startTime: Date.now(),
          metadata: args,
          trace: [],
          state: new Map(),
          events: []
        });
      }
    })

    // Tutorial Assembly Agent
    .addToolAgent('tutorial-assembly', {
      assembleTutorial: async (args: any) => {
        const agent = new TutorialAssemblyAgent(
          builder['orchestrator']['eventBus'],
          builder['orchestrator']['telemetry']
        );
        return agent.execute({
          id: crypto.randomUUID(),
          startTime: Date.now(),
          metadata: args,
          trace: [],
          state: new Map(),
          events: []
        });
      }
    })

    // Add reasoning agent for coordination and error handling
    .addReasoningAgent('coordinator', llmProvider)

    // Add memory agent for state management
    .addMemoryAgent('memory')

    // Define workflow routes
    .route('start', 'repo-analysis')
    .route('extract-concepts', 'concept-extraction')
    .route('plan-tutorial', 'tutorial-planning')
    .route('generate-content', 'content-generation')
    .route('assemble-tutorial', 'tutorial-assembly')
    .route('complete', 'memory')
    .route('error', 'coordinator');

  return builder.build();
}

/**
 * Execute the Code2Tutor workflow with the provided shared store
 */
export async function executeCode2TutorFlow(shared: SharedStore): Promise<any> {
  const workflow = createCode2TutorFlow();

  try {
    // Set up event listeners for monitoring
    const progressListener = workflow.on('agent.status', (event) => {
      console.log(`Agent ${event.payload.agentName}: ${event.payload.message}`);
    });

    const errorListener = workflow.on('error', (event) => {
      console.error('Workflow error:', event.payload);
    });

    // Execute the workflow
    const results = await workflow.execute('start', {
      metadata: { shared },
      state: new Map([['shared', shared]])
    });

    // Clean up listeners
    progressListener();
    errorListener();

    return results;

  } catch (error) {
    console.error('Code2Tutor workflow execution failed:', error);
    throw error;
  }
}

/**
 * Utility function to create a shared store with default values
 */
export function createDefaultSharedStore(overrides: Partial<SharedStore> = {}): SharedStore {
  return {
    user_id: 'anonymous',
    target_audience: 'beginner',
    content_language: 'english',
    tutorial_format: 'guided',
    include_exercises: true,
    include_diagrams: true,
    include_examples: true,
    max_concepts: 8,
    selected_files: [],
    language: 'javascript',
    use_cache: true,
    final_output_dir: 'output',
    ...overrides
  };
}

/**
 * Validate shared store before workflow execution
 */
export function validateSharedStore(shared: SharedStore): string[] {
  const errors: string[] = [];

  if (!shared.user_id) {
    errors.push('user_id is required');
  }

  if (!shared.repo_url && !shared.local_dir) {
    errors.push('Either repo_url or local_dir must be provided');
  }

  if (!shared.project_name) {
    errors.push('project_name is required');
  }

  if (!['beginner', 'intermediate', 'advanced'].includes(shared.target_audience)) {
    errors.push('target_audience must be beginner, intermediate, or advanced');
  }

  if (!['interactive', 'guided', 'self-paced'].includes(shared.tutorial_format)) {
    errors.push('tutorial_format must be interactive, guided, or self-paced');
  }

  if (shared.max_concepts < 3 || shared.max_concepts > 15) {
    errors.push('max_concepts must be between 3 and 15');
  }

  return errors;
}
