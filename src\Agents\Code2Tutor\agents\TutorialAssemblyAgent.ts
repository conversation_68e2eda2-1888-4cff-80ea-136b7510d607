// src/Agents/Code2Tutor/agents/TutorialAssemblyAgent.ts

import { AgentComponent, ExecutionContext, ExecutionResult } from '../../../pangeaflow';
import { SharedStore } from '../types';
import { emitAgentStatus, emitTutorProgress, emitTutorComplete } from '../utils/events';
import { callLlm_openrouter } from '../../shared/callLlm_openrouter';
import { buildPrompt } from '../../../pocketflow/utils/buildPrompt';
import { TUTORIAL_ASSEMBLY_PROMPT } from '../prompts/exerciseGeneration';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

/**
 * TutorialAssemblyAgent - Assembles the final tutorial and saves it
 * 
 * This agent is responsible for:
 * - Combining all generated sections into a cohesive tutorial
 * - Creating table of contents and navigation
 * - Adding introduction and conclusion sections
 * - Saving the tutorial to storage (Supabase)
 * - Generating tutorial metadata for the database
 */
export class TutorialAssemblyAgent extends AgentComponent {
  private maxRetries = 3;
  private currentRetry = 0;

  constructor(eventBus: any, telemetry: any, maxRetries = 3) {
    super('tutorial-assembly', eventBus, telemetry, {
      stage: 'tutorial-assembly',
      progress: 0
    });
    this.maxRetries = maxRetries;
  }

  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    return this.withTelemetry('tutorial-assembly', async () => {
      emitAgentStatus('TutorialAssemblyAgent', 'starting', 0, 'Initializing tutorial assembly');
      
      const shared = context.metadata.shared as SharedStore;
      
      try {
        // Validate inputs
        if (!shared.sections || shared.sections.length === 0) {
          throw new Error('No tutorial sections available for assembly');
        }

        if (!shared.tutorial_structure) {
          throw new Error('No tutorial structure available for assembly');
        }

        emitAgentStatus('TutorialAssemblyAgent', 'processing', 10, 'Preparing tutorial components');
        emitTutorProgress('Tutorial Assembly', 10, 'Assembling tutorial components');

        // Prepare sections content for assembly
        const sectionsContent = this.prepareSectionsContent(shared.sections);
        const tutorialMetadata = shared.tutorial_structure.metadata;

        emitAgentStatus('TutorialAssemblyAgent', 'processing', 30, 'Generating final tutorial structure');

        // Build prompt for tutorial assembly
        const prompt = buildPrompt(TUTORIAL_ASSEMBLY_PROMPT, {
          project_name: shared.project_name || 'Unknown Project',
          tutorial_metadata: this.formatTutorialMetadata(tutorialMetadata),
          sections_content: sectionsContent,
          language_instruction: this.getLanguageInstruction(shared.content_language)
        });

        emitAgentStatus('TutorialAssemblyAgent', 'processing', 50, 'Assembling complete tutorial with LLM');

        // Call LLM for tutorial assembly
        const assembledTutorial = await callLlm_openrouter({
          tutorial_id: shared.tutorial_id,
          prompt,
          temperature: 0.3, // Lower temperature for consistent assembly
          model: "google/gemini-2.5-flash-preview-05-20",
          use_cache: shared.use_cache && this.currentRetry === 0,
          user_id: shared.user_id
        });

        emitAgentStatus('TutorialAssemblyAgent', 'processing', 70, 'Saving tutorial to storage');

        // Save tutorial to storage
        const tutorialId = await this.saveTutorialToStorage(
          assembledTutorial,
          shared,
          tutorialMetadata
        );

        emitAgentStatus('TutorialAssemblyAgent', 'processing', 90, 'Generating tutorial metadata');

        // Update shared store
        shared.final_tutorial = assembledTutorial;
        shared.tutorial_id = tutorialId;

        emitAgentStatus('TutorialAssemblyAgent', 'completed', 100, 'Tutorial assembly completed successfully');
        emitTutorProgress('Tutorial Assembly', 100, 'Tutorial successfully created and saved');

        // Get tutorial URL
        const tutorialUrl = this.getTutorialUrl(tutorialId);

        // Emit completion event
        emitTutorComplete(true, 'Tutorial successfully generated', tutorialId, tutorialUrl);

        this.emit('tutorial.assembled', {
          tutorialId,
          tutorialUrl,
          sectionCount: shared.sections.length
        }, context.id);

        return {
          success: true,
          data: {
            tutorialId,
            tutorialUrl,
            tutorial: assembledTutorial
          },
          events: [],
          nextActions: ['complete'],
          metadata: {
            tutorialId,
            sectionsAssembled: shared.sections.length,
            estimatedTime: tutorialMetadata.estimatedTime
          }
        };

      } catch (error) {
        this.currentRetry++;
        
        if (this.currentRetry < this.maxRetries) {
          emitAgentStatus('TutorialAssemblyAgent', 'processing', 0, `Retry ${this.currentRetry}/${this.maxRetries}: ${error.message}`);
          
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 2000 * this.currentRetry));
          
          // Retry execution
          return this.execute(context);
        }

        emitAgentStatus('TutorialAssemblyAgent', 'error', 0, `Failed after ${this.maxRetries} attempts: ${error.message}`);
        emitTutorComplete(false, `Tutorial assembly failed: ${error.message}`);
        
        return {
          success: false,
          error: error as Error,
          events: [],
          nextActions: ['error'],
          metadata: { stage: 'tutorial-assembly', retries: this.currentRetry }
        };
      }
    });
  }

  /**
   * Prepare sections content for assembly
   */
  private prepareSectionsContent(sections: any[]): string {
    return sections.map((section, index) => {
      let sectionContent = `\n## Section ${index + 1}: ${section.title}\n\n`;
      sectionContent += section.content || 'Content not available';
      
      // Add exercises if present
      if (section.exercises && section.exercises.length > 0) {
        sectionContent += '\n\n### Exercises\n\n';
        section.exercises.forEach((exercise: any, exerciseIndex: number) => {
          sectionContent += `**Exercise ${exerciseIndex + 1}: ${exercise.title}**\n\n`;
          sectionContent += `${exercise.description}\n\n`;
        });
      }

      // Add code examples if present
      if (section.codeExamples && section.codeExamples.length > 0) {
        sectionContent += '\n\n### Code Examples\n\n';
        section.codeExamples.forEach((example: any, exampleIndex: number) => {
          sectionContent += `**Example ${exampleIndex + 1}: ${example.title}**\n\n`;
          sectionContent += `\`\`\`${example.language}\n${example.code}\n\`\`\`\n\n`;
          sectionContent += `${example.explanation}\n\n`;
        });
      }

      return sectionContent;
    }).join('\n---\n');
  }

  /**
   * Format tutorial metadata for the prompt
   */
  private formatTutorialMetadata(metadata: any): string {
    return `
Title: ${metadata.title}
Description: ${metadata.description}
Target Audience: ${metadata.targetAudience}
Estimated Time: ${metadata.estimatedTime} minutes
Learning Objectives:
${metadata.learningObjectives.map((obj: string) => `- ${obj}`).join('\n')}
Prerequisites:
${metadata.prerequisites.map((prereq: string) => `- ${prereq}`).join('\n')}
    `.trim();
  }

  /**
   * Save tutorial to Supabase storage and database
   */
  private async saveTutorialToStorage(
    tutorialContent: string,
    shared: SharedStore,
    metadata: any
  ): Promise<string> {
    const tutorialId = shared.tutorial_id || crypto.randomUUID();
    const fileName = `${tutorialId}/tutorial.md`;

    try {
      // Save tutorial content to storage
      const { error: uploadError } = await supabase.storage
        .from('tutorials')
        .upload(fileName, tutorialContent, {
          contentType: 'text/markdown',
          upsert: true
        });

      if (uploadError) {
        throw new Error(`Failed to upload tutorial: ${uploadError.message}`);
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('tutorials')
        .getPublicUrl(fileName);

      // Save tutorial metadata to database
      const { error: metaError } = await supabase
        .from('tutorial_metadata')
        .insert({
          tutorial_id: tutorialId,
          project_name: shared.project_name || 'Unknown Project',
          index_url: urlData.publicUrl,
          chapter_urls: [urlData.publicUrl], // Single file tutorial
          description: metadata.description,
          repo_url: shared.repo_url,
          language: shared.content_language || 'english',
          user_id: shared.user_id,
          tutorial_type: 'interactive_tutorial', // Distinguish from documentation
          target_audience: metadata.targetAudience,
          estimated_time: metadata.estimatedTime,
          learning_objectives: metadata.learningObjectives,
          prerequisites: metadata.prerequisites
        });

      if (metaError) {
        console.error('Error saving tutorial metadata:', metaError);
        // Don't throw here as the tutorial content was saved successfully
      }

      return tutorialId;

    } catch (error) {
      console.error('Error saving tutorial:', error);
      throw error;
    }
  }

  /**
   * Get tutorial URL for sharing
   */
  private getTutorialUrl(tutorialId: string): string {
    // Construct URL based on your application's routing
    const baseUrl = window.location.origin;
    return `${baseUrl}/tutorial/${tutorialId}`;
  }

  /**
   * Get language instruction based on content language
   */
  private getLanguageInstruction(language: string): string {
    if (language && language.toLowerCase() !== 'english') {
      return `Write all content in ${language}. `;
    }
    return '';
  }
}
