import { useState, useEffect, useRef, useCallback } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { Progress } from "@/components/ui/progress";
import { executeCode2TutorFlow, createDefaultSharedStore } from "@/Agents/Code2Tutor";
import { SharedStore } from "@/Agents/Code2Tutor/types";
import {
  tutorEvents,
  TutorEventType,
  ProgressEvent,
  AgentStatusEvent,
} from "@/Agents/Code2Tutor/utils/events";
import { CheckCircle, Clock, AlertCircle, Loader2, BookOpen, Target, Zap } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";



interface LogEntry {
  timestamp: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
}

interface TutorialStage {
  id: string;
  name: string;
  status: "pending" | "in-progress" | "completed" | "error";
  description: string;
  completedIn: string;
  details: string[];
  concepts?: number;
  exercises?: number;
}

interface AgentStatus {
  currentAgent: string;
  agentProgress: number;
  statusMessage: string;
}

const TutorCreationStatus = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();

  const params = location.state;

  const [progress, setProgress] = useState(0);
  const [logEntries, setLogEntries] = useState<LogEntry[]>([]);
  const [agentStatus, setAgentStatus] = useState<AgentStatus>({
    currentAgent: "",
    agentProgress: 0,
    statusMessage: "Initializing...",
  });

  const logContainerRef = useRef<HTMLDivElement>(null);

  const [tutorialStages, setTutorialStages] = useState<TutorialStage[]>([
    {
      id: "repository-analysis",
      name: "Repository Analysis",
      status: "pending",
      description: "Analyzing repository structure and extracting educational content",
      completedIn: "",
      details: [],
    },
    {
      id: "concept-extraction",
      name: "Learning Concept Extraction",
      status: "pending",
      description: "Identifying key learning concepts and their relationships",
      completedIn: "",
      details: [],
      concepts: 0,
    },
    {
      id: "tutorial-planning",
      name: "Tutorial Structure Planning",
      status: "pending",
      description: "Designing optimal learning progression and dependencies",
      completedIn: "",
      details: [],
    },
    {
      id: "content-generation",
      name: "Interactive Content Generation",
      status: "pending",
      description: "Creating educational content, exercises, and examples",
      completedIn: "",
      details: [],
      exercises: 0,
    },
    {
      id: "tutorial-assembly",
      name: "Tutorial Assembly",
      status: "pending",
      description: "Combining all components into final interactive tutorial",
      completedIn: "",
      details: [],
    },
  ]);

  const flowInitialized = useRef(false);

  useEffect(() => {
    if (!user) {
      return;
    }

    if (!flowInitialized.current) {
      const repoUrl = params?.repoUrl || "https://github.com/example/demo-repo";
      handleGenerateTutorial(repoUrl, params);
      flowInitialized.current = true;
    }
  }, [params, user]);

  useEffect(() => {
    if (logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [logEntries]);

  const getTimestamp = useCallback((): string => {
    return new Date().toLocaleTimeString();
  }, []);

  const addLogEntry = useCallback((message: string, type: LogEntry["type"] = "info") => {
    setLogEntries((prev) => [
      ...prev,
      {
        timestamp: getTimestamp(),
        message,
        type,
      },
    ]);
  }, [getTimestamp]);

  const updateStageStatus = useCallback((
    stageId: string,
    status: TutorialStage["status"],
    details?: string[],
    additionalData?: any
  ) => {
    setTutorialStages((prev) =>
      prev.map((stage) => {
        if (stage.id === stageId) {
          const updatedStage = {
            ...stage,
            status,
            ...(details && { details }),
            ...(status === "completed" && { completedIn: getTimestamp() }),
            ...additionalData,
          };
          return updatedStage;
        }
        return stage;
      })
    );
  }, [getTimestamp]);

  // Set up event listeners for Code2Tutor events
  useEffect(() => {
    // Map agent names to tutorial stages
    const agentToStageMap: Record<string, string> = {
      'RepoAnalysisAgent': 'repository-analysis',
      'ConceptExtractionAgent': 'concept-extraction',
      'TutorialPlanningAgent': 'tutorial-planning',
      'ContentGenerationAgent': 'content-generation',
      'TutorialAssemblyAgent': 'tutorial-assembly'
    };

    // Handle progress events
    const handleProgress = (data: ProgressEvent) => {
      setProgress(data.progress);

      addLogEntry(`Progress: ${data.stage} - ${data.progress}%${
        data.message ? ` - ${data.message}` : ""
      }`, "info");

      // Update stage-specific metrics
      if (data.conceptsFound !== undefined) {
        updateStageStatus("concept-extraction", "in-progress", undefined, {
          concepts: data.conceptsFound
        });
      }

      if (data.sectionsGenerated !== undefined) {
        updateStageStatus("content-generation", "in-progress", undefined, {
          exercises: data.sectionsGenerated
        });
      }
    };

    // Handle agent status events
    const handleAgentStatus = (data: AgentStatusEvent) => {
      const stageId = agentToStageMap[data.agentName];

      setAgentStatus({
        currentAgent: data.agentName,
        agentProgress: data.progress,
        statusMessage: data.message || `${data.status}...`,
      });

      addLogEntry(`${data.agentName}: ${data.message || data.status}`,
        data.status === 'error' ? 'error' : 'info');

      if (stageId) {
        if (data.status === 'starting') {
          updateStageStatus(stageId, "in-progress");
        } else if (data.status === 'completed') {
          updateStageStatus(stageId, "completed", [`${data.agentName} completed successfully`]);
        } else if (data.status === 'error') {
          updateStageStatus(stageId, "error", [`${data.agentName} encountered an error`]);
        }
      }
    };

    // Handle errors
    const handleError = (error: any) => {
      console.error("Code2Tutor workflow error:", error);
      addLogEntry(`💥 Workflow error: ${error.message || error}`, "error");

      toast({
        title: "Workflow Error",
        description: "An error occurred during tutorial generation.",
        variant: "destructive",
      });
    };

    // Handle completion
    const handleComplete = (data: any) => {
      setProgress(100);
      addLogEntry("🎉 Tutorial created successfully!", "success");

      // Log completion data for debugging
      console.log('Tutorial completion data:', data);

      // Mark all stages as completed
      setTutorialStages((prev) =>
        prev.map((stage) => ({
          ...stage,
          status: stage.status === "in-progress" ? "completed" : stage.status,
          completedIn: stage.status === "in-progress" ? getTimestamp() : stage.completedIn
        }))
      );

      toast({
        title: "Tutorial Created!",
        description: "Your interactive tutorial has been generated successfully.",
      });

      // Navigate to tutorial gallery after a delay
      setTimeout(() => {
        navigate("/dashboard/tutor-gallery");
      }, 2000);
    };

    // Subscribe to events
    tutorEvents.on(TutorEventType.PROGRESS, handleProgress);
    tutorEvents.on(TutorEventType.AGENT_STATUS, handleAgentStatus);
    tutorEvents.on(TutorEventType.ERROR, handleError);
    tutorEvents.on(TutorEventType.COMPLETE, handleComplete);

    // Cleanup listeners on unmount
    return () => {
      tutorEvents.removeAllListeners(TutorEventType.PROGRESS);
      tutorEvents.removeAllListeners(TutorEventType.AGENT_STATUS);
      tutorEvents.removeAllListeners(TutorEventType.ERROR);
      tutorEvents.removeAllListeners(TutorEventType.COMPLETE);
    };
  }, [navigate, addLogEntry, updateStageStatus, getTimestamp]);

  const handleGenerateTutorial = async (repoUrl: string, options: any = {}) => {
    try {
      addLogEntry("🚀 Starting Code2Tutor workflow...", "info");

      // Create shared store with user parameters
      const shared: SharedStore = createDefaultSharedStore({
        user_id: user?.id || 'anonymous',
        session_id: `tutor-session-${Date.now()}`,
        tutorial_id: `tutorial-${Date.now()}`,

        // Repository information
        repo_url: repoUrl,
        project_name: options.projectName || undefined,
        github_token: options.githubToken || undefined,

        // Tutorial configuration
        target_audience: options.targetAudience || 'beginner',
        content_language: options.contentLanguage || 'english',
        tutorial_format: options.tutorialFormat || 'guided',
        include_exercises: options.includeExercises !== false,
        include_diagrams: options.includeDiagrams !== false,
        include_examples: options.includeExamples !== false,
        max_concepts: options.maxConcepts || 8,

        // File processing
        selected_files: options.selectedFiles || [],
        language: options.language || 'javascript',
        use_cache: options.useCache !== false,

        // Output configuration
        final_output_dir: options.outputDir || 'output'
      });

      addLogEntry(`� Configuration: ${shared.target_audience} level, ${shared.tutorial_format} format`, "info");
      addLogEntry(`🎯 Repository: ${repoUrl}`, "info");

      // Execute the Code2Tutor workflow
      addLogEntry(`🚀 Starting Code2Tutor workflow execution`, "info");

      try {
        const results = await executeCode2TutorFlow(shared);

        addLogEntry(`📊 Workflow completed with ${results?.length || 0} steps`, "success");
        console.log('Code2Tutor workflow results:', results);

        // Check if the workflow actually succeeded
        if (results && results.length > 0) {
          const lastResult = results[results.length - 1];
          if (lastResult.success) {
            addLogEntry(`✅ Tutorial generation completed successfully`, "success");
          } else {
            addLogEntry(`❌ Workflow completed but final step failed: ${lastResult.error?.message || 'Unknown error'}`, "error");
          }
        } else {
          addLogEntry(`⚠️ Workflow completed but no results returned`, "warning");
        }
      } catch (workflowError) {
        console.error('Code2Tutor workflow failed:', workflowError);
        addLogEntry(`❌ Workflow execution failed: ${workflowError.message}`, "error");
        throw workflowError;
      }

    } catch (error: any) {
      console.error("Error running Code2Tutor workflow:", error);
      addLogEntry(`� Workflow error: ${error.message || error}`, "error");

      // Mark current stage as error
      setTutorialStages((prev) =>
        prev.map((stage) =>
          stage.status === "in-progress"
            ? { ...stage, status: "error" as const, details: [`Error: ${error.message || error}`] }
            : stage
        )
      );

      toast({
        title: "Workflow Error",
        description: "An unexpected error occurred during tutorial generation.",
        variant: "destructive",
      });
    }
  };

  const getStageIcon = (status: TutorialStage["status"]) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case "in-progress":
        return <Loader2 className="h-5 w-5 text-blue-600 animate-spin" />;
      case "error":
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStageColor = (status: TutorialStage["status"]) => {
    switch (status) {
      case "completed":
        return "border-green-200 bg-green-50";
      case "in-progress":
        return "border-blue-200 bg-blue-50";
      case "error":
        return "border-red-200 bg-red-50";
      default:
        return "border-gray-200 bg-gray-50";
    }
  };

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center space-x-3">
          <BookOpen className="h-8 w-8 text-blue-600" />
          <h1 className="text-4xl font-bold text-gray-900">Creating Interactive Tutorial</h1>
        </div>
        <p className="text-xl text-gray-600">
          Transforming your code into an engaging learning experience with exercises and interactive content.
        </p>
        <Link
          to="/dashboard/create-tutor"
          className="text-blue-600 hover:text-blue-700 cursor-pointer"
        >
          <i className="fa-solid fa-arrow-left mr-2"></i>
          Back to configuration
        </Link>
      </div>

      {/* Overall Progress */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Overall Progress</h2>
          <span className="text-lg font-medium text-gray-700">{progress}%</span>
        </div>
        <Progress value={progress} className="h-3 mb-4" />
        
        {agentStatus.currentAgent && (
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <div className="flex items-center space-x-3">
              <Loader2 className="h-5 w-5 text-blue-600 animate-spin" />
              <div>
                <div className="font-semibold text-blue-900">{agentStatus.currentAgent}</div>
                <div className="text-sm text-blue-700">{agentStatus.statusMessage}</div>
                <div className="w-full bg-blue-200 h-2 mt-2 rounded-full overflow-hidden">
                  <div
                    className="bg-blue-600 h-full rounded-full transition-all duration-300"
                    style={{ width: `${agentStatus.agentProgress}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Tutorial Stages */}
        <div className="space-y-6">
          <h2 className="text-xl font-semibold text-gray-900">Tutorial Creation Stages</h2>
          
          <div className="space-y-4">
            {tutorialStages.map((stage) => (
              <div
                key={stage.id}
                className={`p-4 rounded-lg border ${getStageColor(stage.status)}`}
              >
                <div className="flex items-start space-x-3">
                  {getStageIcon(stage.status)}
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold text-gray-900">{stage.name}</h3>
                      {stage.completedIn && (
                        <span className="text-xs text-gray-500">
                          Completed at {stage.completedIn}
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{stage.description}</p>
                    
                    {/* Stage-specific metrics */}
                    {stage.concepts !== undefined && stage.concepts > 0 && (
                      <div className="flex items-center space-x-2 mt-2">
                        <Target className="h-4 w-4 text-blue-600" />
                        <span className="text-sm text-blue-700">{stage.concepts} concepts identified</span>
                      </div>
                    )}
                    
                    {stage.exercises !== undefined && stage.exercises > 0 && (
                      <div className="flex items-center space-x-2 mt-2">
                        <Zap className="h-4 w-4 text-green-600" />
                        <span className="text-sm text-green-700">{stage.exercises} exercises created</span>
                      </div>
                    )}

                    {stage.details.length > 0 && (
                      <div className="mt-2 space-y-1">
                        {stage.details.map((detail, idx) => (
                          <div key={idx} className="text-xs text-gray-600">
                            • {detail}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Activity Log */}
        <div className="space-y-6">
          <h2 className="text-xl font-semibold text-gray-900">Activity Log</h2>
          
          <div
            ref={logContainerRef}
            className="bg-gray-900 text-green-400 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm"
          >
            {logEntries.map((entry, index) => (
              <div key={index} className="mb-1">
                <span className="text-gray-500">[{entry.timestamp}]</span>{" "}
                <span
                  className={
                    entry.type === "error"
                      ? "text-red-400"
                      : entry.type === "success"
                      ? "text-green-400"
                      : entry.type === "warning"
                      ? "text-yellow-400"
                      : "text-blue-400"
                  }
                >
                  {entry.message}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Status Toast */}
      {progress < 100 && agentStatus.currentAgent && (
        <div className="fixed bottom-4 right-4 bg-blue-600 text-white px-6 py-3 rounded-md flex items-center shadow-lg">
          <Loader2 className="h-5 w-5 text-white mr-3 animate-spin" />
          <div>
            <div className="font-semibold">{agentStatus.currentAgent}</div>
            <div className="text-sm">{agentStatus.statusMessage}</div>
            <div className="w-full bg-white/20 h-1 mt-1 rounded-full overflow-hidden">
              <div
                className="bg-white h-full rounded-full transition-all duration-300"
                style={{ width: `${agentStatus.agentProgress}%` }}
              ></div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TutorCreationStatus;
