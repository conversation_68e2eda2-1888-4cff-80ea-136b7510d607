// src/Agents/Code2Tutor/utils/events.ts

/**
 * Event management for Code2Tutor agent
 * Provides a browser-compatible event emitter for progress tracking and status updates
 */

class BrowserEventEmitter {
  private listeners: Map<string, Set<Function>> = new Map();

  emit(event: string, data: any): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  on(event: string, listener: Function): () => void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)!.add(listener);

    // Return unsubscribe function
    return () => {
      this.listeners.get(event)?.delete(listener);
    };
  }

  off(event: string, listener: Function): void {
    this.listeners.get(event)?.delete(listener);
  }

  removeAllListeners(event?: string): void {
    if (event) {
      this.listeners.delete(event);
    } else {
      this.listeners.clear();
    }
  }
}

// Create a dedicated event emitter for the Code2Tutor agent
export const tutorEvents = new BrowserEventEmitter();

// Event types
export enum TutorEventType {
  PROGRESS = 'progress',
  ERROR = 'error',
  COMPLETE = 'complete',
  AGENT_STATUS = 'agent_status',
  CONCEPT_EXTRACTED = 'concept_extracted',
  SECTION_GENERATED = 'section_generated'
}

// Progress event data interface
export interface ProgressEvent {
  stage: string;
  progress: number;
  message?: string;
  conceptsFound?: number;
  sectionsGenerated?: number;
}

// Agent status event data interface
export interface AgentStatusEvent {
  agentName: string;
  status: 'starting' | 'processing' | 'completed' | 'error';
  progress: number;
  message?: string;
  data?: any;
}

// Helper functions to emit events
export const emitTutorProgress = (stage: string, progress: number, message?: string, extra?: Partial<ProgressEvent>): void => {
  tutorEvents.emit(TutorEventType.PROGRESS, { stage, progress, message, ...extra });
};

export const emitAgentStatus = (agentName: string, status: AgentStatusEvent['status'], progress: number, message?: string, data?: any): void => {
  tutorEvents.emit(TutorEventType.AGENT_STATUS, { agentName, status, progress, message, data });
};

export const emitTutorError = (error: Error, stage?: string): void => {
  tutorEvents.emit(TutorEventType.ERROR, { error, stage, timestamp: Date.now() });
};

export const emitTutorComplete = (success: boolean, message: string, tutorialId?: string, tutorialUrl?: string): void => {
  tutorEvents.emit(TutorEventType.COMPLETE, { success, message, tutorialId, tutorialUrl, timestamp: Date.now() });
};

export const emitConceptExtracted = (concept: any): void => {
  tutorEvents.emit(TutorEventType.CONCEPT_EXTRACTED, { concept, timestamp: Date.now() });
};

export const emitSectionGenerated = (section: any): void => {
  tutorEvents.emit(TutorEventType.SECTION_GENERATED, { section, timestamp: Date.now() });
};

// Utility function to get timestamp for logging
export const getTimestamp = (): string => {
  return new Date().toLocaleTimeString();
};
